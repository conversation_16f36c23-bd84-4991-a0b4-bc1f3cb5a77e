// 页面导航功能
document.addEventListener('DOMContentLoaded', function() {
    // 导航链接点击事件
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            
            // 更新导航状态
            navLinks.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // 显示对应页面
            sections.forEach(section => section.classList.remove('active'));
            document.getElementById(targetId).classList.add('active');
        });
    });
    
    // 热力图时间切换
    const timeBtns = document.querySelectorAll('.time-btn');
    timeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            timeBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // 模拟数据更新
            updateHeatmapData(this.dataset.time);
        });
    });
    
    // 线路主题切换
    const themeBtns = document.querySelectorAll('.theme-btn');
    themeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            themeBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // 更新路线预览
            updateRoutePreview(this.dataset.theme);
        });
    });
    
    // 骆岗公园标签页切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 更新标签状态
            tabBtns.forEach(tab => tab.classList.remove('active'));
            this.classList.add('active');
            
            // 显示对应内容
            tabContents.forEach(content => content.classList.remove('active'));
            document.getElementById(targetTab).classList.add('active');
        });
    });
    
    // 全景视图切换
    const viewBtns = document.querySelectorAll('.view-btn');
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            viewBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // 模拟视图切换
            updatePanoramaView(this.dataset.view);
        });
    });
    
    // 游览模式选择
    const modeOptions = document.querySelectorAll('input[name="mode"]');
    modeOptions.forEach(option => {
        option.addEventListener('change', function() {
            updatePlanningMode(this.value);
        });
    });
    
    // 预算滑块
    const budgetSliders = document.querySelectorAll('.budget-slider');
    budgetSliders.forEach(slider => {
        slider.addEventListener('input', function() {
            updateBudget();
        });
    });
    
    // 交通选择
    const transportSelect = document.querySelector('.transport-select');
    if (transportSelect) {
        transportSelect.addEventListener('change', function() {
            updateBudget();
        });
    }
    
    // AI助手功能
    const aiAssistant = document.getElementById('aiAssistant');
    const aiModal = document.getElementById('aiModal');
    const closeAI = document.getElementById('closeAI');
    
    aiAssistant.addEventListener('click', function() {
        aiModal.classList.add('active');
    });
    
    closeAI.addEventListener('click', function() {
        aiModal.classList.remove('active');
    });
    
    // 点击模态框外部关闭
    aiModal.addEventListener('click', function(e) {
        if (e.target === aiModal) {
            aiModal.classList.remove('active');
        }
    });
    
    // 聊天功能
    const chatInput = document.getElementById('chatInput');
    const sendMessage = document.getElementById('sendMessage');
    const chatMessages = document.getElementById('chatMessages');
    
    function addMessage(content, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${isUser ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <p>${content}</p>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function sendChatMessage() {
        const message = chatInput.value.trim();
        if (message) {
            addMessage(message, true);
            chatInput.value = '';
            
            // 模拟AI回复
            setTimeout(() => {
                const responses = [
                    '骆岗公园位于合肥市包河区，可乘坐地铁5号线到骆岗公园站下车。',
                    '今天骆岗公园有无人机表演（10:00）、航空科普讲座（14:00）等活动。',
                    '推荐亲子游路线：航空馆 → 儿童乐园 → 园内餐厅，预计游玩时间2.5小时。',
                    '根据您的问题，我为您推荐以下信息...'
                ];
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addMessage(randomResponse);
            }, 1000);
        }
    }
    
    sendMessage.addEventListener('click', sendChatMessage);
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendChatMessage();
        }
    });
    
    // 建议按钮点击
    const suggestionBtns = document.querySelectorAll('.suggestion-btn');
    suggestionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            chatInput.value = this.textContent;
            aiModal.classList.remove('active');
            // 自动发送消息
            setTimeout(() => {
                sendChatMessage();
            }, 500);
        });
    });
    
    // 图片上传功能
    const imageUpload = document.getElementById('imageUpload');
    if (imageUpload) {
        imageUpload.addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    // 模拟图像识别
                    setTimeout(() => {
                        alert('识别结果：这是骆岗公园的航空馆，建于1952年，是合肥重要的航空历史遗迹。');
                    }, 2000);
                }
            };
            input.click();
        });
    }
    
    // 积分相关功能
    const earnBtns = document.querySelectorAll('.earn-item .btn-secondary');
    earnBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const earnItem = this.closest('.earn-item');
            const pointsText = earnItem.querySelector('.earn-points').textContent;
            const points = parseInt(pointsText.match(/\d+/)[0]);
            
            // 模拟积分获取
            alert(`恭喜您获得${points}积分！`);
            updateUserPoints(points);
        });
    });
    
    const exchangeBtns = document.querySelectorAll('.exchange-item .btn-primary');
    exchangeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const exchangeItem = this.closest('.exchange-item');
            const pointsCost = exchangeItem.querySelector('.points-cost').textContent;
            const cost = parseInt(pointsCost.match(/\d+/)[0]);
            const itemName = exchangeItem.querySelector('h3').textContent;
            
            if (confirm(`确认用${cost}积分兑换${itemName}吗？`)) {
                alert('兑换成功！请到指定地点领取。');
                updateUserPoints(-cost);
            }
        });
    });
    
    // 热点标记点击事件
    const hotspots = document.querySelectorAll('.hotspot');
    hotspots.forEach(hotspot => {
        hotspot.addEventListener('click', function() {
            const info = this.dataset.info;
            alert(`${info}：点击查看详细信息和实时状态`);
        });
    });
    
    // 路线优化按钮
    const routeOptimize = document.querySelector('.route-optimize');
    if (routeOptimize) {
        routeOptimize.addEventListener('click', function() {
            this.textContent = '正在优化...';
            setTimeout(() => {
                this.textContent = '路线已优化';
                alert('路线优化完成！已为您调整为最佳游览顺序，预计可节省30分钟。');
                setTimeout(() => {
                    this.textContent = '优化路线';
                }, 2000);
            }, 2000);
        });
    }
    
    // 初始化页面
    updateBudget();
});

// 辅助函数
function updateHeatmapData(timeType) {
    // 模拟热力图数据更新
    console.log(`更新热力图数据: ${timeType}`);
}

function updateRoutePreview(theme) {
    const routeCard = document.querySelector('.route-card');
    const routes = {
        tech: {
            title: '科创之旅 (推荐)',
            path: ['骆岗公园', '科学岛', '创新馆'],
            time: '6小时',
            price: '¥280/人',
            rating: '4.8分'
        },
        history: {
            title: '历史文化之旅',
            path: ['包公园', '李鸿章故居', '安徽博物院'],
            time: '5小时',
            price: '¥180/人',
            rating: '4.6分'
        },
        nature: {
            title: '生态休闲之旅',
            path: ['滨湖湿地', '骆岗公园', '翡翠湖'],
            time: '4小时',
            price: '¥120/人',
            rating: '4.7分'
        },
        family: {
            title: '亲子游乐之旅',
            path: ['骆岗公园', '海洋世界', '欢乐岛'],
            time: '7小时',
            price: '¥350/人',
            rating: '4.9分'
        }
    };
    
    const route = routes[theme];
    if (route && routeCard) {
        routeCard.querySelector('h3').textContent = route.title;
        
        const pathElements = routeCard.querySelectorAll('.route-stop');
        pathElements.forEach((element, index) => {
            if (route.path[index]) {
                element.textContent = route.path[index];
            }
        });
        
        const infoElements = routeCard.querySelectorAll('.route-info span');
        if (infoElements.length >= 3) {
            infoElements[0].innerHTML = `<i class="fas fa-clock"></i> ${route.time}`;
            infoElements[1].innerHTML = `<i class="fas fa-yen-sign"></i> ${route.price}`;
            infoElements[2].innerHTML = `<i class="fas fa-star"></i> ${route.rating}`;
        }
    }
}

function updatePanoramaView(viewType) {
    const placeholder = document.querySelector('.panorama-placeholder p');
    const views = {
        '3d': '3D实景导览',
        'ar': 'AR增强现实',
        'map': '平面地图视图'
    };
    
    if (placeholder) {
        placeholder.textContent = views[viewType] || '3D实景导览';
    }
}

function updatePlanningMode(mode) {
    // 根据模式更新推荐路线
    const modes = {
        family: { duration: '2.5小时', focus: '亲子互动' },
        elderly: { duration: '3小时', focus: '轻松游览' },
        adventure: { duration: '4小时', focus: '深度探索' }
    };
    
    console.log(`切换到${mode}模式:`, modes[mode]);
}

function updateBudget() {
    const foodSlider = document.querySelector('.budget-slider[data-type="food"]');
    const transportSelect = document.querySelector('.transport-select');
    const budgetTotal = document.querySelector('.budget-total strong');
    
    if (foodSlider && transportSelect && budgetTotal) {
        const foodCost = parseInt(foodSlider.value);
        const transportCost = parseInt(transportSelect.value);
        const total = foodCost + transportCost;
        
        budgetTotal.textContent = `总预算: ¥${total}/人`;
        
        // 更新显示的餐饮价格
        const foodPrice = foodSlider.parentElement.querySelector('.price');
        if (foodPrice) {
            foodPrice.textContent = `¥${foodCost}/人`;
        }
    }
}

function updateUserPoints(change) {
    const pointsDisplay = document.querySelector('.points span');
    const pointsBalance = document.querySelector('.points-balance');
    
    if (pointsDisplay && pointsBalance) {
        let currentPoints = parseInt(pointsDisplay.textContent.match(/\d+/)[0]);
        currentPoints += change;
        
        pointsDisplay.textContent = `积分: ${currentPoints}`;
        pointsBalance.textContent = currentPoints;
        
        // 更新进度条
        const progressFill = document.querySelector('.points-progress .progress-fill');
        if (progressFill) {
            const progress = Math.min((currentPoints % 2000) / 2000 * 100, 100);
            progressFill.style.width = `${progress}%`;
        }
    }
}
