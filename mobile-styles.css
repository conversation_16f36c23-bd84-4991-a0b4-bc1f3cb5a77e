/* 移动端基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #E3F2FD 0%, #F1F8E9 50%, #FFF8E1 100%);
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
}

.app-container {
    max-width: 414px;
    margin: 0 auto;
    background: #fff;
    min-height: 100vh;
    position: relative;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 顶部搜索栏 */
.top-header {
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 414px;
    background: linear-gradient(135deg, #2196F3 0%, #4CAF50 50%, #FFA726 100%);
    padding: 10px 16px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-container {
    padding-top: env(safe-area-inset-top, 20px);
}

.search-box {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 8px 16px;
    backdrop-filter: blur(10px);
}

.search-box i {
    color: #666;
    margin-right: 8px;
}

.search-box input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 16px;
    color: #333;
}

.search-box input::placeholder {
    color: #999;
}

.voice-btn {
    background: none;
    border: none;
    color: #FFA726;
    font-size: 18px;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.voice-btn:active {
    background: rgba(255, 167, 38, 0.1);
    transform: scale(0.95);
}

/* 主要内容区域 */
.main-content {
    padding-top: 80px;
    padding-bottom: 80px;
    min-height: 100vh;
}

.page {
    display: none;
    padding: 16px;
    animation: fadeIn 0.3s ease;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 首页样式 */
.hero-section {
    margin-bottom: 24px;
}

.smart-planning-card {
    background: linear-gradient(135deg, #FFA726 0%, #FF8F00 100%);
    border-radius: 16px;
    padding: 20px;
    color: white;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 8px 24px rgba(255, 167, 38, 0.3);
}

.planning-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.planning-content {
    flex: 1;
}

.planning-content h2 {
    font-size: 18px;
    margin-bottom: 4px;
}

.planning-content p {
    font-size: 14px;
    opacity: 0.9;
}

.planning-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.planning-btn:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
}

/* 功能卡片 */
.feature-cards {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
}

.feature-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.card-header h3 {
    font-size: 16px;
    color: #333;
}

.more-btn {
    color: #FFA726;
    font-size: 14px;
}

/* 活动滑块 */
.activity-slider {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 280px;
}

.activity-item img {
    width: 60px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
}

.activity-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.activity-info p {
    font-size: 12px;
    color: #666;
}

/* 今日推荐 */
.weather-info {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #FFA726;
    font-size: 14px;
}

.recommend-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.recommend-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4CAF50, #8BC34A);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.recommend-content h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.recommend-content p {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.crowd-level {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: bold;
}

.crowd-level.low {
    background: #E8F5E8;
    color: #4CAF50;
}

.crowd-level.medium {
    background: #FFF3E0;
    color: #FF9800;
}

.crowd-level.high {
    background: #FFEBEE;
    color: #F44336;
}

/* 快捷工具 */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
}

.tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px 8px;
    border-radius: 8px;
    background: #F8F9FA;
    transition: all 0.3s ease;
}

.tool-item:active {
    background: #E3F2FD;
    transform: scale(0.95);
}

.tool-item i {
    font-size: 20px;
    color: #FFA726;
}

.tool-item span {
    font-size: 12px;
    color: #666;
}

/* 城市地图 */
.city-map-section {
    margin-bottom: 24px;
}

.city-map-section h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
}

.map-container {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.city-map {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, #E3F2FD, #F1F8E9);
    border-radius: 8px;
    overflow: hidden;
}

.map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
}

.map-placeholder i {
    font-size: 32px;
    margin-bottom: 8px;
    color: #2196F3;
}

.map-marker {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.map-marker:active {
    transform: scale(1.1);
}

.map-marker.luogang {
    top: 30%;
    left: 60%;
}

.map-marker.baogong {
    top: 60%;
    left: 40%;
}

.map-marker.binhu {
    top: 45%;
    left: 80%;
}

.marker-dot {
    width: 12px;
    height: 12px;
    background: #FFA726;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(255, 167, 38, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 167, 38, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 167, 38, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 167, 38, 0); }
}

.map-marker span {
    font-size: 10px;
    color: #333;
    background: white;
    padding: 2px 6px;
    border-radius: 8px;
    margin-top: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

/* 快捷入口 */
.quick-access {
    margin-bottom: 24px;
}

.access-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.access-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.access-item:active {
    background: #F8F9FA;
    transform: scale(0.95);
}

.access-item i {
    font-size: 24px;
    color: #FFA726;
}

.access-item span {
    font-size: 12px;
    color: #666;
    text-align: center;
}

/* 探索页样式 */
.explore-tabs {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.explore-tabs .tab-item {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    transition: all 0.3s ease;
}

.explore-tabs .tab-item.active {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
}

/* 筛选器 */
.filter-section {
    margin-bottom: 16px;
}

.filter-chips {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 4px;
}

.filter-chip {
    background: white;
    border: 1px solid #E0E0E0;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.filter-chip.active {
    background: #FFA726;
    color: white;
    border-color: #FFA726;
}

/* 内容卡片 */
.content-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.content-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.content-card:active {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-image {
    position: relative;
    height: 120px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-badges {
    position: absolute;
    top: 8px;
    left: 8px;
    display: flex;
    gap: 4px;
}

.badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: bold;
}

.badge.free {
    background: #4CAF50;
    color: white;
}

.badge.paid {
    background: #FF9800;
    color: white;
}

.badge.family {
    background: #E91E63;
    color: white;
}

.badge.culture {
    background: #9C27B0;
    color: white;
}

.card-content {
    padding: 12px;
}

.card-content h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.stars {
    font-size: 12px;
}

.score {
    font-size: 12px;
    color: #FFA726;
    font-weight: bold;
}

.description {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.4;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.distance {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #666;
}

.distance i {
    color: #FFA726;
}

.detail-btn {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.detail-btn:active {
    transform: scale(0.95);
}

/* 底部导航栏 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 414px;
    background: white;
    display: flex;
    padding: 8px 0;
    padding-bottom: calc(8px + env(safe-area-inset-bottom, 0px));
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 4px;
    color: #999;
    transition: all 0.3s ease;
}

.nav-item.active {
    color: #FFA726;
}

.nav-item i {
    font-size: 20px;
}

.nav-item span {
    font-size: 10px;
}

/* 悬浮助手 */
.floating-assistant {
    position: fixed;
    bottom: 100px;
    right: 16px;
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    padding: 12px 16px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 16px rgba(255, 167, 38, 0.4);
    z-index: 999;
    transition: all 0.3s ease;
}

.floating-assistant:active {
    transform: scale(0.95);
}

.floating-assistant i {
    font-size: 18px;
}

.floating-assistant span {
    font-size: 14px;
    font-weight: bold;
}

/* 响应式适配 */
@media (max-width: 375px) {
    .app-container {
        max-width: 375px;
    }
    
    .top-header {
        max-width: 375px;
    }
    
    .bottom-nav {
        max-width: 375px;
    }
    
    .access-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
    }
    
    .tools-grid {
        gap: 8px;
    }
}

/* 行程页样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    font-size: 20px;
    color: #333;
}

.add-itinerary-btn {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.add-itinerary-btn:active {
    transform: scale(0.95);
}

.current-itinerary {
    margin-bottom: 24px;
}

.itinerary-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.itinerary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.itinerary-header h3 {
    font-size: 16px;
    color: #333;
}

.itinerary-date {
    font-size: 12px;
    color: #666;
    background: #F8F9FA;
    padding: 4px 8px;
    border-radius: 8px;
}

.itinerary-progress {
    margin-bottom: 16px;
}

.progress-bar {
    background: #E0E0E0;
    height: 4px;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FFA726);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #666;
}

.itinerary-spots {
    margin-bottom: 16px;
}

.spot-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #F0F0F0;
}

.spot-item:last-child {
    border-bottom: none;
}

.spot-time {
    background: #FFA726;
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.spot-item.completed .spot-time {
    background: #4CAF50;
}

.spot-info {
    flex: 1;
}

.spot-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.spot-info p {
    font-size: 12px;
    color: #666;
}

.spot-status {
    color: #FFA726;
    font-size: 16px;
}

.spot-item.completed .spot-status {
    color: #4CAF50;
}

.itinerary-actions {
    display: flex;
    gap: 12px;
}

.action-btn {
    flex: 1;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    border: 1px solid #E0E0E0;
    background: white;
    color: #666;
    transition: all 0.3s ease;
}

.action-btn.primary {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    border-color: #FFA726;
}

.action-btn:active {
    transform: scale(0.95);
}

/* 电子票夹 */
.ticket-wallet {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.wallet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.wallet-header h3 {
    font-size: 16px;
    color: #333;
}

.ticket-count {
    font-size: 12px;
    color: #FFA726;
    background: rgba(255, 167, 38, 0.1);
    padding: 4px 8px;
    border-radius: 8px;
}

.ticket-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #F8F9FA;
    border-radius: 8px;
}

.ticket-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.ticket-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.ticket-info p {
    font-size: 12px;
    color: #666;
}

/* 发现页样式 */
.discover-tabs {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.discover-tabs .tab-item {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    transition: all 0.3s ease;
}

.discover-tabs .tab-item.active {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
}

.community-content {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.content-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.content-filters .filter-chip {
    background: #F8F9FA;
    border: 1px solid #E0E0E0;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

.content-filters .filter-chip.active {
    background: #FFA726;
    color: white;
    border-color: #FFA726;
}

.community-posts {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.post-card {
    border: 1px solid #F0F0F0;
    border-radius: 8px;
    padding: 12px;
    background: #FAFAFA;
}

.post-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    flex: 1;
}

.user-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.post-time {
    font-size: 11px;
    color: #999;
}

.post-tag {
    background: #FFA726;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: bold;
}

.post-content h3 {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
}

.post-content p {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 8px;
}

.post-images {
    margin-bottom: 12px;
}

.post-images img {
    width: 100%;
    max-width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
}

.post-actions {
    display: flex;
    gap: 16px;
}

.post-actions .action-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.post-actions .action-btn:active {
    background: #F0F0F0;
}

.post-actions .action-btn i {
    font-size: 14px;
}

/* 我的页面样式 */
.profile-header {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    padding: 20px 16px;
    border-radius: 0 0 20px 20px;
    margin: -16px -16px 20px -16px;
}

.profile-header .user-info {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.user-avatar.large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.user-avatar.large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details h3 {
    font-size: 18px;
    margin-bottom: 4px;
}

.user-details p {
    font-size: 14px;
    opacity: 0.9;
}

.level-progress .progress-bar {
    background: rgba(255, 255, 255, 0.2);
    height: 6px;
    border-radius: 3px;
    margin-bottom: 8px;
}

.level-progress .progress-fill {
    background: white;
}

.level-progress .progress-text {
    font-size: 12px;
    opacity: 0.8;
}

.profile-menu {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.menu-section {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-bottom: 1px solid #F0F0F0;
    transition: all 0.3s ease;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:active {
    background: #F8F9FA;
}

.menu-item i:first-child {
    width: 20px;
    color: #FFA726;
    font-size: 16px;
}

.menu-item span:first-of-type {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.menu-value {
    font-size: 14px;
    color: #FFA726;
    font-weight: bold;
}

.menu-item i:last-child {
    color: #CCC;
    font-size: 12px;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 20px;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 16px;
    width: 100%;
    max-width: 360px;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideUp 0.3s ease;
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:active {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* 规划表单样式 */
.planning-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 14px;
    color: #333;
    font-weight: bold;
}

.day-selector {
    display: flex;
    gap: 8px;
}

.day-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid #E0E0E0;
    background: white;
    color: #666;
    border-radius: 20px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.day-btn.active {
    background: #FFA726;
    color: white;
    border-color: #FFA726;
}

.preference-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-item {
    padding: 6px 12px;
    border: 1px solid #E0E0E0;
    background: white;
    color: #666;
    border-radius: 16px;
    font-size: 12px;
    transition: all 0.3s ease;
}

.tag-item.active {
    background: #FFA726;
    color: white;
    border-color: #FFA726;
}

.budget-slider {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.budget-slider input[type="range"] {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #E0E0E0;
    outline: none;
    -webkit-appearance: none;
}

.budget-slider input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #FFA726;
    cursor: pointer;
}

.budget-display {
    text-align: center;
    font-size: 16px;
    color: #FFA726;
    font-weight: bold;
}

.generate-btn {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.generate-btn:active {
    transform: scale(0.95);
}
