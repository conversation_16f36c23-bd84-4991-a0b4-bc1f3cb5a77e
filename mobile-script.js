// 移动端乐游合肥应用脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化应用
    initializeApp();
    
    // 底部导航切换
    const navItems = document.querySelectorAll('.nav-item');
    const pages = document.querySelectorAll('.page');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetPage = this.dataset.page;
            
            // 更新导航状态
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // 切换页面
            pages.forEach(page => page.classList.remove('active'));
            document.getElementById(targetPage).classList.add('active');
            
            // 页面切换动画
            const activePage = document.getElementById(targetPage);
            activePage.style.transform = 'translateX(10px)';
            activePage.style.opacity = '0';
            
            setTimeout(() => {
                activePage.style.transform = 'translateX(0)';
                activePage.style.opacity = '1';
            }, 50);
        });
    });
    
    // 智能规划功能
    const planningBtn = document.getElementById('startPlanning');
    const planningModal = document.getElementById('planningModal');
    const closeBtn = planningModal.querySelector('.close-btn');
    
    planningBtn.addEventListener('click', function() {
        planningModal.classList.add('active');
        // 添加触觉反馈
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    });
    
    closeBtn.addEventListener('click', function() {
        planningModal.classList.remove('active');
    });
    
    // 点击模态框外部关闭
    planningModal.addEventListener('click', function(e) {
        if (e.target === planningModal) {
            planningModal.classList.remove('active');
        }
    });
    
    // 规划表单交互
    const dayBtns = document.querySelectorAll('.day-btn');
    dayBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            dayBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    const tagItems = document.querySelectorAll('.tag-item');
    tagItems.forEach(tag => {
        tag.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    });
    
    const budgetRange = document.getElementById('budgetRange');
    const budgetValue = document.getElementById('budgetValue');
    
    if (budgetRange && budgetValue) {
        budgetRange.addEventListener('input', function() {
            budgetValue.textContent = this.value;
        });
    }
    
    // 生成行程按钮
    const generateBtn = document.querySelector('.generate-btn');
    if (generateBtn) {
        generateBtn.addEventListener('click', function() {
            this.textContent = '正在生成...';
            this.disabled = true;
            
            // 模拟AI生成过程
            setTimeout(() => {
                this.textContent = '生成完成！';
                setTimeout(() => {
                    planningModal.classList.remove('active');
                    showToast('专属行程已生成，请查看行程页面');
                    this.textContent = '生成专属行程';
                    this.disabled = false;
                }, 1000);
            }, 2000);
        });
    }
    
    // 语音搜索功能
    const voiceBtn = document.getElementById('voiceSearch');
    const globalSearch = document.getElementById('globalSearch');
    
    voiceBtn.addEventListener('click', function() {
        if ('webkitSpeechRecognition' in window) {
            const recognition = new webkitSpeechRecognition();
            recognition.lang = 'zh-CN';
            recognition.continuous = false;
            recognition.interimResults = false;
            
            recognition.onstart = function() {
                voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
                voiceBtn.style.background = '#F44336';
                showToast('正在听取您的语音...');
            };
            
            recognition.onresult = function(event) {
                const result = event.results[0][0].transcript;
                globalSearch.value = result;
                showToast(`识别结果：${result}`);
                // 自动搜索
                performSearch(result);
            };
            
            recognition.onend = function() {
                voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                voiceBtn.style.background = '';
            };
            
            recognition.onerror = function() {
                showToast('语音识别失败，请重试');
                voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                voiceBtn.style.background = '';
            };
            
            recognition.start();
        } else {
            showToast('您的浏览器不支持语音识别');
        }
    });
    
    // 搜索功能
    globalSearch.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch(this.value);
        }
    });
    
    // 快捷工具点击
    const toolItems = document.querySelectorAll('.tool-item');
    toolItems.forEach(tool => {
        tool.addEventListener('click', function() {
            const toolType = this.dataset.tool;
            handleToolClick(toolType);
        });
    });
    
    // 快捷入口点击
    const accessItems = document.querySelectorAll('.access-item');
    accessItems.forEach(item => {
        item.addEventListener('click', function() {
            const page = this.dataset.page;
            const tab = this.dataset.tab;
            const tool = this.dataset.tool;
            
            if (page) {
                // 切换到指定页面
                navItems.forEach(nav => nav.classList.remove('active'));
                document.querySelector(`[data-page="${page}"]`).classList.add('active');
                
                pages.forEach(p => p.classList.remove('active'));
                document.getElementById(page).classList.add('active');
                
                // 如果有指定标签页，也要切换
                if (tab) {
                    const tabItems = document.querySelectorAll(`#${page} .tab-item`);
                    tabItems.forEach(t => t.classList.remove('active'));
                    document.querySelector(`#${page} [data-tab="${tab}"]`).classList.add('active');
                }
            } else if (tool) {
                handleToolClick(tool);
            }
        });
    });
    
    // 地图标记点击
    const mapMarkers = document.querySelectorAll('.map-marker');
    mapMarkers.forEach(marker => {
        marker.addEventListener('click', function() {
            const spot = this.dataset.spot;
            showSpotInfo(spot);
        });
    });
    
    // 探索页标签切换
    const exploreTabs = document.querySelectorAll('.explore-tabs .tab-item');
    exploreTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            exploreTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            const tabType = this.dataset.tab;
            loadExploreContent(tabType);
        });
    });
    
    // 筛选器点击
    const filterChips = document.querySelectorAll('.filter-chip');
    filterChips.forEach(chip => {
        chip.addEventListener('click', function() {
            if (this.parentElement.classList.contains('filter-chips')) {
                // 探索页筛选器
                const siblings = this.parentElement.querySelectorAll('.filter-chip');
                siblings.forEach(s => s.classList.remove('active'));
                this.classList.add('active');
            } else {
                // 发现页筛选器
                this.classList.toggle('active');
            }
        });
    });
    
    // 景点卡片点击
    const attractionCards = document.querySelectorAll('.attraction-card');
    attractionCards.forEach(card => {
        card.addEventListener('click', function() {
            const attractionId = this.dataset.id;
            if (attractionId === 'luogang') {
                showLuogangDetail();
            } else {
                showAttractionDetail(attractionId);
            }
        });
    });
    
    // 发现页标签切换
    const discoverTabs = document.querySelectorAll('.discover-tabs .tab-item');
    discoverTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            discoverTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            const tabType = this.dataset.tab;
            loadDiscoverContent(tabType);
        });
    });
    
    // 悬浮助手点击
    const floatingAssistant = document.getElementById('floatingAssistant');
    floatingAssistant.addEventListener('click', function() {
        showAIAssistant();
    });
    
    // 触摸手势支持
    let startX, startY, endX, endY;
    
    document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchend', function(e) {
        endX = e.changedTouches[0].clientX;
        endY = e.changedTouches[0].clientY;
        
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        
        // 左右滑动切换页面
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
            if (deltaX > 0) {
                // 向右滑动
                switchToPreviousPage();
            } else {
                // 向左滑动
                switchToNextPage();
            }
        }
    });
});

// 辅助函数
function initializeApp() {
    // 检查设备支持
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js').catch(console.error);
    }
    
    // 设置安全区域
    if (window.CSS && CSS.supports('padding-top: env(safe-area-inset-top)')) {
        document.body.style.paddingTop = 'env(safe-area-inset-top)';
        document.body.style.paddingBottom = 'env(safe-area-inset-bottom)';
    }
    
    // 初始化定位
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            position => {
                console.log('位置获取成功:', position.coords);
                updateLocationBasedContent(position.coords);
            },
            error => {
                console.log('位置获取失败:', error);
                showToast('位置获取失败，部分功能可能受限');
            }
        );
    }
}

function performSearch(query) {
    if (!query.trim()) return;
    
    showToast(`正在搜索：${query}`);
    
    // 模拟搜索结果
    setTimeout(() => {
        if (query.includes('骆岗') || query.includes('公园')) {
            showToast('找到骆岗公园相关信息');
            // 跳转到骆岗公园详情
            showLuogangDetail();
        } else {
            showToast(`找到${Math.floor(Math.random() * 20) + 1}个相关结果`);
        }
    }, 1000);
}

function handleToolClick(toolType) {
    switch (toolType) {
        case 'camera':
            openCamera();
            break;
        case 'points':
            showPointsCenter();
            break;
        case 'navigation':
            startNavigation();
            break;
        case 'emergency':
            showEmergencyHelp();
            break;
        case 'more':
            showMoreTools();
            break;
        default:
            showToast(`${toolType} 功能开发中...`);
    }
}

function openCamera() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
                showToast('相机已启动，请拍摄景点照片');
                // 这里可以添加相机界面
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());
                    showToast('识别结果：这是骆岗公园的航空馆！');
                }, 3000);
            })
            .catch(error => {
                showToast('无法访问相机，请检查权限设置');
            });
    } else {
        showToast('您的设备不支持相机功能');
    }
}

function showSpotInfo(spot) {
    const spotInfo = {
        luogang: {
            name: '骆岗公园',
            description: '超大规模城市中央公园，航空记忆，生态绿心',
            status: '人流较少，适合游玩',
            distance: '2.3km'
        },
        baogong: {
            name: '包公园',
            description: '纪念北宋名臣包拯的主题公园',
            status: '人流适中',
            distance: '5.1km'
        },
        binhu: {
            name: '滨湖湿地',
            description: '生态湿地公园，观鸟胜地',
            status: '人流较少，推荐游玩',
            distance: '8.2km'
        }
    };
    
    const info = spotInfo[spot];
    if (info) {
        showToast(`${info.name} - ${info.status}`);
    }
}

function showLuogangDetail() {
    // 创建骆岗公园详情页面
    const detailHTML = `
        <div class="attraction-detail" id="luogangDetail">
            <div class="detail-header">
                <button class="back-btn" onclick="closeLuogangDetail()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2>骆岗公园</h2>
                <button class="share-btn">
                    <i class="fas fa-share"></i>
                </button>
            </div>
            <div class="detail-content">
                <div class="hero-image">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzgwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzgwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzRDQUY1MCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+6aqG5biX5YWs5ZutPC90ZXh0Pjwvc3ZnPg==" alt="骆岗公园">
                    <div class="image-overlay">
                        <button class="vr-btn">
                            <i class="fas fa-vr-cardboard"></i>
                            VR全景
                        </button>
                    </div>
                </div>
                <div class="basic-info">
                    <h3>骆岗公园 (骆岗中央公园)</h3>
                    <div class="rating">⭐⭐⭐⭐⭐ 4.8分</div>
                    <p class="description">超大规模城市中央公园，航空记忆，生态绿心，文化艺术高地</p>
                </div>
                <div class="action-buttons">
                    <button class="action-btn primary">
                        <i class="fas fa-navigation"></i>
                        一键导航
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-ticket-alt"></i>
                        购买门票
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-plus"></i>
                        加入行程
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', detailHTML);
    document.getElementById('luogangDetail').style.display = 'block';
}

function closeLuogangDetail() {
    const detail = document.getElementById('luogangDetail');
    if (detail) {
        detail.remove();
    }
}

function loadExploreContent(tabType) {
    showToast(`正在加载${tabType}内容...`);
    // 这里可以根据不同的标签加载不同的内容
}

function loadDiscoverContent(tabType) {
    showToast(`切换到${tabType}页面`);
    // 这里可以根据不同的标签加载不同的内容
}

function showAIAssistant() {
    showToast('小乐：您好！有什么可以帮助您的吗？');
    // 这里可以打开AI助手对话界面
}

function showToast(message) {
    // 创建toast提示
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 9999;
        max-width: 80%;
        text-align: center;
        animation: toastFadeIn 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'toastFadeOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 2000);
}

function updateLocationBasedContent(coords) {
    // 根据位置更新内容
    console.log('更新基于位置的内容', coords);
}

function switchToPreviousPage() {
    // 切换到上一个页面的逻辑
}

function switchToNextPage() {
    // 切换到下一个页面的逻辑
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes toastFadeIn {
        from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    }
    
    @keyframes toastFadeOut {
        from { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        to { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    }
    
    .attraction-detail {
        position: fixed;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        max-width: 414px;
        height: 100vh;
        background: white;
        z-index: 2000;
        overflow-y: auto;
        display: none;
    }
    
    .detail-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        background: linear-gradient(135deg, #FFA726, #FF8F00);
        color: white;
    }
    
    .back-btn, .share-btn {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }
    
    .back-btn:active, .share-btn:active {
        background: rgba(255, 255, 255, 0.2);
    }
    
    .detail-content {
        padding: 16px;
    }
    
    .hero-image {
        position: relative;
        margin-bottom: 16px;
    }
    
    .hero-image img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 12px;
    }
    
    .image-overlay {
        position: absolute;
        top: 12px;
        right: 12px;
    }
    
    .vr-btn {
        background: rgba(0, 0, 0, 0.6);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .basic-info {
        margin-bottom: 20px;
    }
    
    .basic-info h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 8px;
    }
    
    .basic-info .rating {
        font-size: 14px;
        margin-bottom: 8px;
    }
    
    .basic-info .description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
    }
    
    .action-buttons {
        display: flex;
        gap: 12px;
    }
    
    .action-buttons .action-btn {
        flex: 1;
        padding: 12px;
        border-radius: 25px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        transition: all 0.3s ease;
    }
    
    .action-buttons .action-btn.primary {
        background: linear-gradient(135deg, #FFA726, #FF8F00);
        color: white;
        border: none;
    }
    
    .action-buttons .action-btn:not(.primary) {
        background: white;
        color: #FFA726;
        border: 1px solid #FFA726;
    }
    
    .action-buttons .action-btn:active {
        transform: scale(0.95);
    }
`;
document.head.appendChild(style);
