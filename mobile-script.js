// 移动端乐游合肥应用脚本 - 严格按照设计方案实现
document.addEventListener('DOMContentLoaded', function() {
    // 初始化应用
    initializeApp();

    // 初始化搜索建议
    initializeSearchSuggestions();

    // 初始化AI功能
    initializeAIFeatures();

    // 初始化地图功能
    initializeMapFeatures();

    // 初始化骆岗公园详情页
    initializeLuogangDetail();
    
    // 底部导航切换
    const navItems = document.querySelectorAll('.nav-item');
    const pages = document.querySelectorAll('.page');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetPage = this.dataset.page;
            
            // 更新导航状态
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // 切换页面
            pages.forEach(page => page.classList.remove('active'));
            document.getElementById(targetPage).classList.add('active');
            
            // 页面切换动画
            const activePage = document.getElementById(targetPage);
            activePage.style.transform = 'translateX(10px)';
            activePage.style.opacity = '0';
            
            setTimeout(() => {
                activePage.style.transform = 'translateX(0)';
                activePage.style.opacity = '1';
            }, 50);
        });
    });
    
    // 智能规划功能
    const planningBtn = document.getElementById('startPlanning');
    const planningModal = document.getElementById('planningModal');
    const closeBtn = planningModal.querySelector('.close-btn');
    
    planningBtn.addEventListener('click', function() {
        planningModal.classList.add('active');
        // 添加触觉反馈
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    });
    
    closeBtn.addEventListener('click', function() {
        planningModal.classList.remove('active');
    });
    
    // 点击模态框外部关闭
    planningModal.addEventListener('click', function(e) {
        if (e.target === planningModal) {
            planningModal.classList.remove('active');
        }
    });
    
    // 规划表单交互
    const dayBtns = document.querySelectorAll('.day-btn');
    dayBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            dayBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    const tagItems = document.querySelectorAll('.tag-item');
    tagItems.forEach(tag => {
        tag.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    });
    
    const budgetRange = document.getElementById('budgetRange');
    const budgetValue = document.getElementById('budgetValue');
    
    if (budgetRange && budgetValue) {
        budgetRange.addEventListener('input', function() {
            budgetValue.textContent = this.value;
        });
    }
    
    // 生成行程按钮
    const generateBtn = document.querySelector('.generate-btn');
    if (generateBtn) {
        generateBtn.addEventListener('click', function() {
            this.textContent = '正在生成...';
            this.disabled = true;
            
            // 模拟AI生成过程
            setTimeout(() => {
                this.textContent = '生成完成！';
                setTimeout(() => {
                    planningModal.classList.remove('active');
                    showToast('专属行程已生成，请查看行程页面');
                    this.textContent = '生成专属行程';
                    this.disabled = false;
                }, 1000);
            }, 2000);
        });
    }
    
    // 小乐语音助手功能 (增强版)
    const voiceBtn = document.getElementById('voiceSearch');
    const globalSearch = document.getElementById('globalSearch');

    voiceBtn.addEventListener('click', function() {
        if ('webkitSpeechRecognition' in window) {
            const recognition = new webkitSpeechRecognition();
            recognition.lang = 'zh-CN';
            recognition.continuous = false;
            recognition.interimResults = false;

            recognition.onstart = function() {
                voiceBtn.innerHTML = '<i class="fas fa-stop"></i><span class="voice-indicator recording"></span>';
                voiceBtn.style.background = '#F44336';
                voiceBtn.style.animation = 'voicePulse 1s infinite';
                showToast('🎤 小乐正在听取您的语音...');

                // 添加语音波形动画
                const voiceWave = document.querySelector('.voice-wave-indicator');
                if (voiceWave) {
                    voiceWave.style.display = 'block';
                }
            };

            recognition.onresult = function(event) {
                const result = event.results[0][0].transcript;
                globalSearch.value = result;
                showToast(`🤖 小乐识别结果：${result}`);

                // AI智能回复
                setTimeout(() => {
                    const aiResponse = generateAIResponse(result);
                    showAIResponse(aiResponse);
                }, 1000);

                // 自动搜索
                performSearch(result);
            };

            recognition.onend = function() {
                voiceBtn.innerHTML = '<i class="fas fa-microphone"></i><span class="voice-indicator"></span>';
                voiceBtn.style.background = '';
                voiceBtn.style.animation = '';

                const voiceWave = document.querySelector('.voice-wave-indicator');
                if (voiceWave) {
                    voiceWave.style.display = 'none';
                }
            };

            recognition.onerror = function() {
                showToast('🚫 语音识别失败，请重试');
                voiceBtn.innerHTML = '<i class="fas fa-microphone"></i><span class="voice-indicator"></span>';
                voiceBtn.style.background = '';
                voiceBtn.style.animation = '';
            };

            recognition.start();

            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        } else {
            showToast('您的浏览器不支持语音识别功能');
        }
    });
    
    // 搜索功能
    globalSearch.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch(this.value);
        }
    });
    
    // 快捷工具点击
    const toolItems = document.querySelectorAll('.tool-item');
    toolItems.forEach(tool => {
        tool.addEventListener('click', function() {
            const toolType = this.dataset.tool;
            handleToolClick(toolType);
        });
    });
    
    // 快捷入口点击
    const accessItems = document.querySelectorAll('.access-item');
    accessItems.forEach(item => {
        item.addEventListener('click', function() {
            const page = this.dataset.page;
            const tab = this.dataset.tab;
            const tool = this.dataset.tool;
            
            if (page) {
                // 切换到指定页面
                navItems.forEach(nav => nav.classList.remove('active'));
                document.querySelector(`[data-page="${page}"]`).classList.add('active');
                
                pages.forEach(p => p.classList.remove('active'));
                document.getElementById(page).classList.add('active');
                
                // 如果有指定标签页，也要切换
                if (tab) {
                    const tabItems = document.querySelectorAll(`#${page} .tab-item`);
                    tabItems.forEach(t => t.classList.remove('active'));
                    document.querySelector(`#${page} [data-tab="${tab}"]`).classList.add('active');
                }
            } else if (tool) {
                handleToolClick(tool);
            }
        });
    });
    
    // 地图标记点击
    const mapMarkers = document.querySelectorAll('.map-marker');
    mapMarkers.forEach(marker => {
        marker.addEventListener('click', function() {
            const spot = this.dataset.spot;
            showSpotInfo(spot);
        });
    });
    
    // 探索页标签切换
    const exploreTabs = document.querySelectorAll('.explore-tabs .tab-item');
    exploreTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            exploreTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            const tabType = this.dataset.tab;
            loadExploreContent(tabType);
        });
    });
    
    // 筛选器点击
    const filterChips = document.querySelectorAll('.filter-chip');
    filterChips.forEach(chip => {
        chip.addEventListener('click', function() {
            if (this.parentElement.classList.contains('filter-chips')) {
                // 探索页筛选器
                const siblings = this.parentElement.querySelectorAll('.filter-chip');
                siblings.forEach(s => s.classList.remove('active'));
                this.classList.add('active');
            } else {
                // 发现页筛选器
                this.classList.toggle('active');
            }
        });
    });
    
    // 景点卡片点击
    const attractionCards = document.querySelectorAll('.attraction-card');
    attractionCards.forEach(card => {
        card.addEventListener('click', function() {
            const attractionId = this.dataset.id;
            if (attractionId === 'luogang') {
                showLuogangDetail();
            } else {
                showAttractionDetail(attractionId);
            }
        });
    });
    
    // 发现页标签切换
    const discoverTabs = document.querySelectorAll('.discover-tabs .tab-item');
    discoverTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            discoverTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            const tabType = this.dataset.tab;
            loadDiscoverContent(tabType);
        });
    });
    
    // 悬浮助手点击
    const floatingAssistant = document.getElementById('floatingAssistant');
    floatingAssistant.addEventListener('click', function() {
        showAIAssistant();
    });
    
    // 触摸手势支持
    let startX, startY, endX, endY;
    
    document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchend', function(e) {
        endX = e.changedTouches[0].clientX;
        endY = e.changedTouches[0].clientY;
        
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        
        // 左右滑动切换页面
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
            if (deltaX > 0) {
                // 向右滑动
                switchToPreviousPage();
            } else {
                // 向左滑动
                switchToNextPage();
            }
        }
    });
});

// 辅助函数
function initializeApp() {
    // 检查设备支持
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js').catch(console.error);
    }
    
    // 设置安全区域
    if (window.CSS && CSS.supports('padding-top: env(safe-area-inset-top)')) {
        document.body.style.paddingTop = 'env(safe-area-inset-top)';
        document.body.style.paddingBottom = 'env(safe-area-inset-bottom)';
    }
    
    // 初始化定位
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            position => {
                console.log('位置获取成功:', position.coords);
                updateLocationBasedContent(position.coords);
            },
            error => {
                console.log('位置获取失败:', error);
                showToast('位置获取失败，部分功能可能受限');
            }
        );
    }
}

function performSearch(query) {
    if (!query.trim()) return;
    
    showToast(`正在搜索：${query}`);
    
    // 模拟搜索结果
    setTimeout(() => {
        if (query.includes('骆岗') || query.includes('公园')) {
            showToast('找到骆岗公园相关信息');
            // 跳转到骆岗公园详情
            showLuogangDetail();
        } else {
            showToast(`找到${Math.floor(Math.random() * 20) + 1}个相关结果`);
        }
    }, 1000);
}

function handleToolClick(toolType) {
    switch (toolType) {
        case 'camera':
            openCamera();
            break;
        case 'points':
            showPointsCenter();
            break;
        case 'navigation':
            startNavigation();
            break;
        case 'emergency':
            showEmergencyHelp();
            break;
        case 'more':
            showMoreTools();
            break;
        default:
            showToast(`${toolType} 功能开发中...`);
    }
}

function openCamera() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
                showToast('相机已启动，请拍摄景点照片');
                // 这里可以添加相机界面
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());
                    showToast('识别结果：这是骆岗公园的航空馆！');
                }, 3000);
            })
            .catch(error => {
                showToast('无法访问相机，请检查权限设置');
            });
    } else {
        showToast('您的设备不支持相机功能');
    }
}

function showSpotInfo(spot) {
    const spotInfo = {
        luogang: {
            name: '骆岗公园',
            description: '超大规模城市中央公园，航空记忆，生态绿心',
            status: '人流较少，适合游玩',
            distance: '2.3km'
        },
        baogong: {
            name: '包公园',
            description: '纪念北宋名臣包拯的主题公园',
            status: '人流适中',
            distance: '5.1km'
        },
        binhu: {
            name: '滨湖湿地',
            description: '生态湿地公园，观鸟胜地',
            status: '人流较少，推荐游玩',
            distance: '8.2km'
        }
    };
    
    const info = spotInfo[spot];
    if (info) {
        showToast(`${info.name} - ${info.status}`);
    }
}

function showLuogangDetail() {
    // 创建骆岗公园详情页面
    const detailHTML = `
        <div class="attraction-detail" id="luogangDetail">
            <div class="detail-header">
                <button class="back-btn" onclick="closeLuogangDetail()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2>骆岗公园</h2>
                <button class="share-btn">
                    <i class="fas fa-share"></i>
                </button>
            </div>
            <div class="detail-content">
                <div class="hero-image">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzgwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzgwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzRDQUY1MCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+6aqG5biX5YWs5ZutPC90ZXh0Pjwvc3ZnPg==" alt="骆岗公园">
                    <div class="image-overlay">
                        <button class="vr-btn">
                            <i class="fas fa-vr-cardboard"></i>
                            VR全景
                        </button>
                    </div>
                </div>
                <div class="basic-info">
                    <h3>骆岗公园 (骆岗中央公园)</h3>
                    <div class="rating">⭐⭐⭐⭐⭐ 4.8分</div>
                    <p class="description">超大规模城市中央公园，航空记忆，生态绿心，文化艺术高地</p>
                </div>
                <div class="action-buttons">
                    <button class="action-btn primary">
                        <i class="fas fa-navigation"></i>
                        一键导航
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-ticket-alt"></i>
                        购买门票
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-plus"></i>
                        加入行程
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', detailHTML);
    document.getElementById('luogangDetail').style.display = 'block';
}

function closeLuogangDetail() {
    const detail = document.getElementById('luogangDetail');
    if (detail) {
        detail.remove();
    }
}

function loadExploreContent(tabType) {
    showToast(`正在加载${tabType}内容...`);
    // 这里可以根据不同的标签加载不同的内容
}

function loadDiscoverContent(tabType) {
    showToast(`切换到${tabType}页面`);
    // 这里可以根据不同的标签加载不同的内容
}

function showAIAssistant() {
    showToast('小乐：您好！有什么可以帮助您的吗？');
    // 这里可以打开AI助手对话界面
}

function showToast(message) {
    // 创建toast提示
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 9999;
        max-width: 80%;
        text-align: center;
        animation: toastFadeIn 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'toastFadeOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 2000);
}

function updateLocationBasedContent(coords) {
    // 根据位置更新内容
    console.log('更新基于位置的内容', coords);
}

function switchToPreviousPage() {
    // 切换到上一个页面的逻辑
}

function switchToNextPage() {
    // 切换到下一个页面的逻辑
}

// 新增初始化函数
function initializeSearchSuggestions() {
    const globalSearch = document.getElementById('globalSearch');
    const searchSuggestions = document.getElementById('searchSuggestions');

    globalSearch.addEventListener('focus', function() {
        searchSuggestions.classList.add('active');
    });

    globalSearch.addEventListener('blur', function() {
        setTimeout(() => {
            searchSuggestions.classList.remove('active');
        }, 200);
    });

    // 搜索建议点击
    const suggestionItems = document.querySelectorAll('.suggestion-item');
    suggestionItems.forEach(item => {
        item.addEventListener('click', function() {
            globalSearch.value = this.textContent;
            searchSuggestions.classList.remove('active');
            performSearch(this.textContent);
        });
    });
}

function initializeAIFeatures() {
    // AI工具点击
    const aiToolItems = document.querySelectorAll('.ai-tool-item');
    aiToolItems.forEach(item => {
        item.addEventListener('click', function() {
            const tool = this.dataset.tool;
            handleAITool(tool);
        });
    });

    // 积分活动交互
    const exchangeBtns = document.querySelectorAll('.exchange-btn');
    exchangeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const pointsItem = this.closest('.points-item');
            const itemName = pointsItem.querySelector('h4').textContent;
            showPointsExchange(itemName);
        });
    });

    const taskBtns = document.querySelectorAll('.task-btn');
    taskBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const pointsItem = this.closest('.points-item');
            const taskName = pointsItem.querySelector('h4').textContent;
            handlePointsTask(taskName);
        });
    });
}

function initializeMapFeatures() {
    // 地图控制按钮
    const mapControlBtns = document.querySelectorAll('.map-control-btn');
    mapControlBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            mapControlBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            const layer = this.dataset.layer;
            toggleMapLayer(layer);
        });
    });

    // 地图标记点击 (增强版)
    const mapMarkers = document.querySelectorAll('.map-marker');
    mapMarkers.forEach(marker => {
        marker.addEventListener('click', function() {
            const spot = this.dataset.spot;
            if (spot === 'luogang') {
                // 骆岗公园特殊动效
                this.style.animation = 'featuredBounce 0.6s ease';
                setTimeout(() => {
                    this.style.animation = '';
                    showLuogangDetail();
                }, 600);
            } else {
                showSpotInfo(spot);
            }
        });
    });
}

function initializeLuogangDetail() {
    // 骆岗公园专属助手
    const parkAssistant = document.getElementById('parkAssistant');
    if (parkAssistant) {
        parkAssistant.addEventListener('click', function() {
            showLuogangAI();
        });
    }

    // VR入口按钮
    const vrEntranceBtn = document.getElementById('vrEntranceBtn');
    if (vrEntranceBtn) {
        vrEntranceBtn.addEventListener('click', function() {
            startVRExperience();
        });
    }

    // 模块折叠功能
    window.toggleModule = function(moduleId) {
        const module = document.getElementById(moduleId);
        module.classList.toggle('expanded');
    };
}

// AI功能处理函数
function handleAITool(tool) {
    switch (tool) {
        case 'camera':
            startAIImageRecognition();
            break;
        case 'voice':
            startVoiceAssistant();
            break;
        default:
            showToast(`🤖 ${tool} 功能启动中...`);
    }
}

function startAIImageRecognition() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        showToast('📷 启动AI看图识景功能...');

        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
                showToast('🎯 请将镜头对准景点拍摄');

                // 模拟AI识别过程
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());

                    const recognitionResults = [
                        {
                            name: '骆岗公园-旧航站楼',
                            confidence: '98%',
                            description: '建于1952年，是合肥重要的航空历史遗迹',
                            location: '骆岗公园航站楼区',
                            tips: '最佳拍照时间：下午4-6点，逆光效果佳'
                        },
                        {
                            name: '骆岗公园-中央大草坪',
                            confidence: '95%',
                            description: '占地面积约50公顷的超大草坪',
                            location: '骆岗公园中心区域',
                            tips: '适合野餐、放风筝，周末有音乐节活动'
                        }
                    ];

                    const result = recognitionResults[Math.floor(Math.random() * recognitionResults.length)];
                    showAIRecognitionResult(result);
                }, 3000);
            })
            .catch(error => {
                showToast('❌ 无法访问相机，请检查权限设置');
            });
    } else {
        showToast('❌ 您的设备不支持相机功能');
    }
}

function showAIRecognitionResult(result) {
    const resultHTML = `
        <div class="ai-recognition-result">
            <div class="result-header">
                <h3>🎯 AI识别结果</h3>
                <span class="confidence">准确率: ${result.confidence}</span>
            </div>
            <div class="result-content">
                <h4>${result.name}</h4>
                <p class="description">${result.description}</p>
                <div class="result-details">
                    <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${result.location}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-lightbulb"></i>
                        <span>${result.tips}</span>
                    </div>
                </div>
                <div class="result-actions">
                    <button class="result-btn primary" onclick="navigateToSpot('${result.name}')">
                        <i class="fas fa-navigation"></i>
                        前往导航
                    </button>
                    <button class="result-btn secondary" onclick="generateTravelNote('${result.name}')">
                        <i class="fas fa-edit"></i>
                        生成游记
                    </button>
                </div>
            </div>
        </div>
    `;

    showModal('AI识别结果', resultHTML);
}

function generateAIResponse(query) {
    const responses = {
        '骆岗公园': '🌟 骆岗公园是合肥最大的城市中央公园，有航空主题展览、大草坪和VR体验。今天天气不错，人流较少，很适合游玩！',
        '天气': '☀️ 今天合肥天气晴朗，24°C，微风，非常适合户外活动。建议您去骆岗公园或滨湖湿地走走。',
        '停车': '🅿️ 骆岗公园P3停车场还有87个空位，P1停车场156个空位充足。建议选择P1停车场，离主要景点更近。',
        '美食': '🍽️ 推荐骆岗公园内的航空主题咖啡厅，人均35元，还有文创冰淇淋站。园外500米有正宗徽菜馆。',
        '活动': '🎪 今晚19:30有骆岗公园主题灯光秀，明天10:00有无人机表演，都是免费观看的热门活动！'
    };

    for (let key in responses) {
        if (query.includes(key)) {
            return responses[key];
        }
    }

    return '🤖 小乐正在为您查询相关信息，请稍等...';
}

function showAIResponse(response) {
    const aiResponseHTML = `
        <div class="ai-response-card">
            <div class="ai-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="ai-message">
                <p>${response}</p>
            </div>
        </div>
    `;

    // 创建临时显示区域
    const responseDiv = document.createElement('div');
    responseDiv.className = 'ai-response-overlay';
    responseDiv.innerHTML = aiResponseHTML;
    responseDiv.style.cssText = `
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 3000;
        max-width: 90%;
        animation: slideDown 0.3s ease;
    `;

    document.body.appendChild(responseDiv);

    setTimeout(() => {
        responseDiv.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(responseDiv);
        }, 300);
    }, 4000);
}

// 地图功能
function toggleMapLayer(layer) {
    const layers = document.querySelectorAll('.map-layer');
    layers.forEach(l => l.classList.remove('active'));

    const targetLayer = document.querySelector(`.${layer}-layer`);
    if (targetLayer) {
        targetLayer.classList.add('active');
    }

    showToast(`🗺️ 已切换到${getLayerName(layer)}视图`);
}

function getLayerName(layer) {
    const names = {
        'base': '基础',
        'facilities': '设施',
        'activities': '活动',
        'routes': '路线'
    };
    return names[layer] || layer;
}

// 骆岗公园专属功能
function showLuogangDetail() {
    const detailPage = document.getElementById('luogangDetailPage');
    if (detailPage) {
        detailPage.classList.add('active');
        detailPage.style.display = 'block';

        // 添加进入动画
        setTimeout(() => {
            detailPage.scrollTop = 0;
        }, 100);
    }
}

function closeLuogangDetail() {
    const detailPage = document.getElementById('luogangDetailPage');
    if (detailPage) {
        detailPage.classList.remove('active');
        setTimeout(() => {
            detailPage.style.display = 'none';
        }, 300);
    }
}

function showLuogangAI() {
    const luogangResponses = [
        '🌟 欢迎来到骆岗公园！我是您的专属导游小乐。这里有什么想了解的吗？',
        '✈️ 骆岗公园的前身是合肥骆岗机场，现在保留了航站楼作为历史纪念。',
        '🎪 今晚的灯光秀在中央大草坪举行，建议您提前30分钟到达占个好位置。',
        '🚗 从您当前位置到P3停车场步行约5分钟，那里还有87个空位。',
        '📷 最佳拍照点是旧航站楼前的观景台，下午4-6点光线最好。'
    ];

    const response = luogangResponses[Math.floor(Math.random() * luogangResponses.length)];
    showAIResponse(response);
}

function startVRExperience() {
    showToast('🥽 启动VR全景体验...');

    // 模拟VR加载过程
    setTimeout(() => {
        showToast('🌟 VR体验已准备就绪！请佩戴VR设备或使用手机陀螺仪体验。');

        // 这里可以集成真实的VR库
        const vrHTML = `
            <div class="vr-experience">
                <div class="vr-controls">
                    <button class="vr-btn" onclick="closeVR()">退出VR</button>
                </div>
                <div class="vr-scene">
                    <div class="vr-placeholder">
                        <i class="fas fa-vr-cardboard"></i>
                        <h3>骆岗公园VR全景</h3>
                        <p>360°沉浸式体验航空主题公园</p>
                        <div class="vr-hotspots">
                            <div class="vr-hotspot" style="top: 30%; left: 40%;">航站楼</div>
                            <div class="vr-hotspot" style="top: 60%; left: 70%;">大草坪</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        showModal('VR全景体验', vrHTML);
    }, 2000);
}

// 积分功能
function showPointsExchange(itemName) {
    showToast(`🎁 正在为您兑换${itemName}...`);

    setTimeout(() => {
        showToast(`✅ ${itemName}兑换成功！请到指定地点领取。`);
        updateUserPoints(-1000); // 扣除积分
    }, 1500);
}

function handlePointsTask(taskName) {
    if (taskName.includes('上传游记')) {
        showToast('📝 正在打开游记编辑器...');
        setTimeout(() => {
            showToast('✅ 游记上传成功！审核通过后将获得50乐豆。');
            updateUserPoints(50);
        }, 2000);
    } else {
        showToast(`📋 正在执行任务：${taskName}`);
    }
}

// 通用模态框函数
function showModal(title, content) {
    const modalHTML = `
        <div class="custom-modal" id="customModal">
            <div class="modal-overlay" onclick="closeModal()"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function closeModal() {
    const modal = document.getElementById('customModal');
    if (modal) {
        modal.remove();
    }
}

window.closeVR = function() {
    closeModal();
};

// 添加CSS动画和样式
const style = document.createElement('style');
style.textContent = `
    /* 语音动画 */
    @keyframes voicePulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    @keyframes featuredBounce {
        0%, 100% { transform: scale(1); }
        25% { transform: scale(1.2) rotate(5deg); }
        75% { transform: scale(1.1) rotate(-5deg); }
    }

    @keyframes slideDown {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        to { opacity: 1; transform: translateX(-50%) translateY(0); }
    }

    @keyframes slideUp {
        from { opacity: 1; transform: translateX(-50%) translateY(0); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
    }

    /* AI响应卡片 */
    .ai-response-card {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        background: white;
        padding: 16px;
        border-radius: 16px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        border-left: 4px solid var(--hefei-blue);
        max-width: 320px;
    }

    .ai-response-card .ai-avatar {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--hefei-blue), var(--eco-green));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
        flex-shrink: 0;
    }

    .ai-response-card .ai-message p {
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
    }

    /* AI识别结果 */
    .ai-recognition-result {
        background: white;
        border-radius: 16px;
        overflow: hidden;
    }

    .result-header {
        background: linear-gradient(135deg, var(--hefei-blue), var(--eco-green));
        color: white;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .result-header h3 {
        margin: 0;
        font-size: 16px;
    }

    .confidence {
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: bold;
    }

    .result-content {
        padding: 20px;
    }

    .result-content h4 {
        font-size: 18px;
        color: #333;
        margin-bottom: 8px;
    }

    .result-content .description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 16px;
    }

    .result-details {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 20px;
    }

    .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #666;
    }

    .detail-item i {
        color: var(--vibrant-orange);
        width: 16px;
    }

    .result-actions {
        display: flex;
        gap: 12px;
    }

    .result-btn {
        flex: 1;
        padding: 12px;
        border-radius: 12px;
        border: none;
        font-size: 14px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        transition: all 0.3s ease;
    }

    .result-btn.primary {
        background: linear-gradient(135deg, var(--vibrant-orange), #FF8F00);
        color: white;
    }

    .result-btn.secondary {
        background: white;
        color: var(--hefei-blue);
        border: 2px solid var(--hefei-blue);
    }

    .result-btn:active {
        transform: scale(0.95);
    }

    /* VR体验 */
    .vr-experience {
        background: #000;
        color: white;
        border-radius: 16px;
        overflow: hidden;
        min-height: 300px;
    }

    .vr-controls {
        padding: 16px;
        background: rgba(255, 255, 255, 0.1);
        text-align: right;
    }

    .vr-btn {
        background: var(--vibrant-orange);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
    }

    .vr-scene {
        position: relative;
        height: 250px;
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .vr-placeholder {
        text-align: center;
    }

    .vr-placeholder i {
        font-size: 48px;
        margin-bottom: 16px;
        color: var(--vibrant-orange);
    }

    .vr-placeholder h3 {
        font-size: 20px;
        margin-bottom: 8px;
    }

    .vr-placeholder p {
        font-size: 14px;
        opacity: 0.8;
        margin-bottom: 20px;
    }

    .vr-hotspots {
        position: relative;
        width: 200px;
        height: 100px;
        margin: 0 auto;
    }

    .vr-hotspot {
        position: absolute;
        background: var(--vibrant-orange);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        cursor: pointer;
        animation: pulse 2s infinite;
    }

    /* 自定义模态框 */
    .custom-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 3000;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
    }

    .modal-container {
        background: white;
        border-radius: 16px;
        max-width: 90%;
        max-height: 80vh;
        overflow: hidden;
        position: relative;
        z-index: 1;
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: scale(0.9) translateY(20px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    .modal-header {
        background: linear-gradient(135deg, var(--hefei-blue), var(--eco-green));
        color: white;
        padding: 16px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 16px;
    }

    .modal-close {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .modal-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .modal-body {
        padding: 20px;
        max-height: 60vh;
        overflow-y: auto;
    }

    /* 语音指示器 */
    .voice-indicator.recording {
        background: #F44336 !important;
        animation: recordingPulse 1s infinite;
    }

    @keyframes recordingPulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .voice-wave-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 2px solid #F44336;
        border-radius: 50%;
        animation: voiceWaveExpand 1s infinite;
        display: none;
    }

    @keyframes voiceWaveExpand {
        0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
    }

    /* 浮动助手增强 */
    .floating-assistant {
        position: fixed;
        bottom: 100px;
        right: 16px;
        background: linear-gradient(135deg, var(--vibrant-orange), #FF8F00);
        color: white;
        padding: 12px 16px;
        border-radius: 25px;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 16px rgba(255, 167, 38, 0.4);
        z-index: 999;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .floating-assistant:active {
        transform: scale(0.95);
    }

    .assistant-avatar {
        position: relative;
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
    }

    .voice-wave-indicator {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 12px;
        height: 12px;
        background: #4CAF50;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    .assistant-name {
        font-size: 14px;
        font-weight: bold;
    }

    .assistant-status {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #4CAF50;
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 9px;
        font-weight: bold;
    }

    /* 骆岗公园专属助手 */
    .park-assistant {
        position: fixed;
        bottom: 160px;
        right: 16px;
        background: linear-gradient(135deg, var(--eco-green), #8BC34A);
        color: white;
        padding: 12px 16px;
        border-radius: 25px;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
        z-index: 998;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
    }

    .park-assistant:active {
        transform: scale(0.95);
    }

    .assistant-pulse {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 12px;
        height: 12px;
        background: #FFA726;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }
`;
document.head.appendChild(style);
    @keyframes toastFadeIn {
        from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    }
    
    @keyframes toastFadeOut {
        from { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        to { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    }
    
    .attraction-detail {
        position: fixed;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        max-width: 414px;
        height: 100vh;
        background: white;
        z-index: 2000;
        overflow-y: auto;
        display: none;
    }
    
    .detail-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        background: linear-gradient(135deg, #FFA726, #FF8F00);
        color: white;
    }
    
    .back-btn, .share-btn {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }
    
    .back-btn:active, .share-btn:active {
        background: rgba(255, 255, 255, 0.2);
    }
    
    .detail-content {
        padding: 16px;
    }
    
    .hero-image {
        position: relative;
        margin-bottom: 16px;
    }
    
    .hero-image img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 12px;
    }
    
    .image-overlay {
        position: absolute;
        top: 12px;
        right: 12px;
    }
    
    .vr-btn {
        background: rgba(0, 0, 0, 0.6);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .basic-info {
        margin-bottom: 20px;
    }
    
    .basic-info h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 8px;
    }
    
    .basic-info .rating {
        font-size: 14px;
        margin-bottom: 8px;
    }
    
    .basic-info .description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
    }
    
    .action-buttons {
        display: flex;
        gap: 12px;
    }
    
    .action-buttons .action-btn {
        flex: 1;
        padding: 12px;
        border-radius: 25px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        transition: all 0.3s ease;
    }
    
    .action-buttons .action-btn.primary {
        background: linear-gradient(135deg, #FFA726, #FF8F00);
        color: white;
        border: none;
    }
    
    .action-buttons .action-btn:not(.primary) {
        background: white;
        color: #FFA726;
        border: 1px solid #FFA726;
    }
    
    .action-buttons .action-btn:active {
        transform: scale(0.95);
    }
`;
document.head.appendChild(style);
