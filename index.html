<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥智慧文旅平台</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 头部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-map-marked-alt"></i>
                <span>合肥智慧文旅</span>
            </div>
            <nav class="nav">
                <a href="#overview" class="nav-link active">城市概览</a>
                <a href="#luogang" class="nav-link">骆岗公园</a>
                <a href="#services" class="nav-link">智能服务</a>
                <a href="#points" class="nav-link">积分中心</a>
            </nav>
            <div class="user-info">
                <span class="points">积分: 1250</span>
                <div class="avatar">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- 智能助手浮动按钮 -->
    <div class="ai-assistant" id="aiAssistant">
        <i class="fas fa-robot"></i>
        <span>智能助手</span>
    </div>

    <!-- 主要内容区域 -->
    <main class="main">
        <!-- 城市概览页面 -->
        <section id="overview" class="section active">
            <div class="container">
                <h1 class="section-title">合肥文旅实时概览</h1>
                
                <!-- 实时数据面板 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>今日游客</h3>
                            <p class="stat-number">28,450</p>
                            <span class="stat-change positive">+12.5%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>热门景区</h3>
                            <p class="stat-number">23</p>
                            <span class="stat-change">4A级以上</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>活动进行中</h3>
                            <p class="stat-number">8</p>
                            <span class="stat-change">场活动</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3>今日消费</h3>
                            <p class="stat-number">¥2.1M</p>
                            <span class="stat-change positive">+8.3%</span>
                        </div>
                    </div>
                </div>

                <!-- 热力图谱 -->
                <div class="heatmap-section">
                    <h2>实时客流热力图</h2>
                    <div class="heatmap-controls">
                        <button class="time-btn active" data-time="hour">小时</button>
                        <button class="time-btn" data-time="day">日</button>
                        <button class="time-btn" data-time="week">周</button>
                    </div>
                    <div class="heatmap-container">
                        <div class="heatmap-item high">
                            <span class="location">骆岗公园</span>
                            <span class="visitors">3,820人</span>
                            <div class="capacity-bar">
                                <div class="capacity-fill" style="width: 84%"></div>
                            </div>
                            <span class="status warning">84% 橙色预警</span>
                        </div>
                        <div class="heatmap-item medium">
                            <span class="location">包公园</span>
                            <span class="visitors">2,150人</span>
                            <div class="capacity-bar">
                                <div class="capacity-fill" style="width: 65%"></div>
                            </div>
                            <span class="status normal">65% 正常</span>
                        </div>
                        <div class="heatmap-item low">
                            <span class="location">滨湖湿地</span>
                            <span class="visitors">890人</span>
                            <div class="capacity-bar">
                                <div class="capacity-fill" style="width: 30%"></div>
                            </div>
                            <span class="status low">30% 推荐</span>
                        </div>
                    </div>
                </div>

                <!-- 线路工厂 -->
                <div class="route-factory">
                    <h2>智能线路工厂</h2>
                    <div class="route-themes">
                        <button class="theme-btn active" data-theme="tech">科创之旅</button>
                        <button class="theme-btn" data-theme="history">历史文化</button>
                        <button class="theme-btn" data-theme="nature">生态休闲</button>
                        <button class="theme-btn" data-theme="family">亲子游乐</button>
                    </div>
                    <div class="route-preview">
                        <div class="route-card">
                            <h3>科创之旅 (推荐)</h3>
                            <div class="route-path">
                                <span class="route-stop">骆岗公园</span>
                                <i class="fas fa-arrow-right"></i>
                                <span class="route-stop">科学岛</span>
                                <i class="fas fa-arrow-right"></i>
                                <span class="route-stop">创新馆</span>
                            </div>
                            <div class="route-info">
                                <span><i class="fas fa-clock"></i> 6小时</span>
                                <span><i class="fas fa-yen-sign"></i> ¥280/人</span>
                                <span><i class="fas fa-star"></i> 4.8分</span>
                            </div>
                            <button class="btn-primary">立即规划</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 骆岗公园详细页面 -->
        <section id="luogang" class="section">
            <div class="container">
                <div class="park-header">
                    <h1>骆岗公园 - 智慧景区示范</h1>
                    <div class="park-status">
                        <div class="status-item">
                            <span class="status-label">实时客流</span>
                            <span class="status-value warning">3,820人 / 4,500人</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">停车位</span>
                            <span class="status-value">P3停车场 87个空位</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">天气</span>
                            <span class="status-value">晴 24°C</span>
                        </div>
                    </div>
                </div>

                <!-- 功能标签页 -->
                <div class="tabs">
                    <button class="tab-btn active" data-tab="panorama">全景导览</button>
                    <button class="tab-btn" data-tab="planning">智能规划</button>
                    <button class="tab-btn" data-tab="experience">深度体验</button>
                    <button class="tab-btn" data-tab="services">园区服务</button>
                </div>

                <!-- 全景导览 -->
                <div id="panorama" class="tab-content active">
                    <div class="panorama-container">
                        <div class="view-controls">
                            <button class="view-btn active" data-view="3d">3D模型</button>
                            <button class="view-btn" data-view="ar">AR模式</button>
                            <button class="view-btn" data-view="map">平面地图</button>
                        </div>
                        <div class="panorama-viewer">
                            <div class="panorama-placeholder">
                                <i class="fas fa-cube"></i>
                                <p>3D实景导览</p>
                                <small>拖拽查看园区全貌</small>
                            </div>
                            <div class="panorama-hotspots">
                                <div class="hotspot" style="top: 30%; left: 40%;" data-info="航空馆">
                                    <i class="fas fa-plane"></i>
                                </div>
                                <div class="hotspot" style="top: 60%; left: 70%;" data-info="儿童乐园">
                                    <i class="fas fa-child"></i>
                                </div>
                                <div class="hotspot" style="top: 45%; left: 20%;" data-info="咖啡厅">
                                    <i class="fas fa-coffee"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 智能规划 -->
                <div id="planning" class="tab-content">
                    <div class="planning-container">
                        <div class="planning-sidebar">
                            <h3>游览模式</h3>
                            <div class="mode-selector">
                                <label class="mode-option">
                                    <input type="radio" name="mode" value="family" checked>
                                    <span>亲子模式</span>
                                </label>
                                <label class="mode-option">
                                    <input type="radio" name="mode" value="elderly">
                                    <span>老人模式</span>
                                </label>
                                <label class="mode-option">
                                    <input type="radio" name="mode" value="adventure">
                                    <span>暴走模式</span>
                                </label>
                            </div>

                            <h3>预算设置</h3>
                            <div class="budget-calculator">
                                <div class="budget-item">
                                    <span>门票</span>
                                    <span class="price">免费</span>
                                </div>
                                <div class="budget-item">
                                    <span>餐饮</span>
                                    <input type="range" min="50" max="200" value="120" class="budget-slider" data-type="food">
                                    <span class="price">¥120/人</span>
                                </div>
                                <div class="budget-item">
                                    <span>交通</span>
                                    <select class="transport-select">
                                        <option value="15">观光车 ¥15</option>
                                        <option value="0">步行 免费</option>
                                        <option value="30">电瓶车 ¥30</option>
                                    </select>
                                </div>
                                <div class="budget-total">
                                    <strong>总预算: ¥135/人</strong>
                                </div>
                            </div>
                        </div>

                        <div class="planning-main">
                            <div class="route-timeline">
                                <h3>推荐路线 (2.5小时)</h3>
                                <div class="timeline-item">
                                    <div class="timeline-time">10:00</div>
                                    <div class="timeline-content">
                                        <h4>航空馆</h4>
                                        <p>参观时间: 60分钟</p>
                                        <span class="timeline-tag">历史文化</span>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-time">11:30</div>
                                    <div class="timeline-content">
                                        <h4>儿童乐园</h4>
                                        <p>游玩时间: 90分钟</p>
                                        <span class="timeline-tag">亲子互动</span>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-time">13:00</div>
                                    <div class="timeline-content">
                                        <h4>园内餐厅</h4>
                                        <p>用餐时间: 45分钟</p>
                                        <span class="timeline-tag">美食体验</span>
                                    </div>
                                </div>
                            </div>
                            <button class="btn-primary route-optimize">优化路线</button>
                        </div>
                    </div>
                </div>

                <!-- 深度体验 -->
                <div id="experience" class="tab-content">
                    <div class="experience-grid">
                        <div class="experience-card">
                            <div class="experience-icon">
                                <i class="fas fa-helicopter"></i>
                            </div>
                            <h3>航空主题VR导览</h3>
                            <p>沉浸式体验老机场历史变迁</p>
                            <div class="experience-info">
                                <span><i class="fas fa-clock"></i> 20分钟</span>
                                <span><i class="fas fa-yen-sign"></i> ¥30</span>
                            </div>
                            <button class="btn-secondary">立即预约</button>
                        </div>
                        <div class="experience-card">
                            <div class="experience-icon">
                                <i class="fas fa-camera"></i>
                            </div>
                            <h3>看图识景</h3>
                            <p>上传照片识别园内景点获取详细信息</p>
                            <div class="upload-area" id="imageUpload">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击或拖拽上传图片</p>
                            </div>
                        </div>
                        <div class="experience-card">
                            <div class="experience-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <h3>智能植物识别</h3>
                            <p>扫描植物获取详细科普信息</p>
                            <button class="btn-secondary">开启相机</button>
                        </div>
                    </div>
                </div>

                <!-- 园区服务 -->
                <div id="services" class="tab-content">
                    <div class="services-grid">
                        <div class="service-category">
                            <h3><i class="fas fa-car"></i> 交通服务</h3>
                            <ul>
                                <li>地铁5号线B口 (步行200米)</li>
                                <li>公交站: 骆岗公园站</li>
                                <li>停车场: P1-P5 (实时空位查询)</li>
                            </ul>
                        </div>
                        <div class="service-category">
                            <h3><i class="fas fa-concierge-bell"></i> 便民服务</h3>
                            <ul>
                                <li>母婴室 → 3号厅</li>
                                <li>医疗站 → 南门</li>
                                <li>失物招领 → 游客中心</li>
                                <li>轮椅租借 → 各入口</li>
                            </ul>
                        </div>
                        <div class="service-category">
                            <h3><i class="fas fa-calendar-check"></i> 今日活动</h3>
                            <ul>
                                <li>10:00 停机坪无人机表演</li>
                                <li>14:00 航空科普讲座</li>
                                <li>16:00 儿童手工制作</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 智能服务页面 -->
        <section id="services" class="section">
            <div class="container">
                <h1 class="section-title">智能服务中心</h1>

                <!-- AI问答引擎 -->
                <div class="ai-chat-section">
                    <h2>AI智能问答</h2>
                    <div class="chat-container">
                        <div class="chat-messages" id="chatMessages">
                            <div class="message bot">
                                <div class="message-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="message-content">
                                    <p>您好！我是合肥文旅智能助手，可以为您提供景点介绍、路线规划、实时信息等服务。请问有什么可以帮助您的？</p>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-area">
                            <div class="input-modes">
                                <button class="mode-btn active" data-mode="text">
                                    <i class="fas fa-keyboard"></i>
                                </button>
                                <button class="mode-btn" data-mode="voice">
                                    <i class="fas fa-microphone"></i>
                                </button>
                                <button class="mode-btn" data-mode="image">
                                    <i class="fas fa-camera"></i>
                                </button>
                            </div>
                            <input type="text" id="chatInput" placeholder="输入您的问题..." class="chat-input">
                            <button id="sendMessage" class="send-btn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 快捷服务 -->
                <div class="quick-services">
                    <h2>快捷服务</h2>
                    <div class="services-grid">
                        <div class="service-card">
                            <div class="service-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <h3>动态导航</h3>
                            <p>实时避堵路线规划</p>
                            <button class="btn-secondary">开始导航</button>
                        </div>
                        <div class="service-card">
                            <div class="service-icon">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <h3>在线预约</h3>
                            <p>景点门票、活动预约</p>
                            <button class="btn-secondary">立即预约</button>
                        </div>
                        <div class="service-card">
                            <div class="service-icon">
                                <i class="fas fa-weather-sun"></i>
                            </div>
                            <h3>天气预报</h3>
                            <p>实时天气及出行建议</p>
                            <div class="weather-info">
                                <span>晴 24°C</span>
                                <small>适宜出行</small>
                            </div>
                        </div>
                        <div class="service-card">
                            <div class="service-icon">
                                <i class="fas fa-ambulance"></i>
                            </div>
                            <h3>应急服务</h3>
                            <p>紧急求助、医疗服务</p>
                            <button class="btn-danger">紧急求助</button>
                        </div>
                    </div>
                </div>

                <!-- 智能游记生成 -->
                <div class="travel-journal">
                    <h2>智能游记生成</h2>
                    <div class="journal-creator">
                        <div class="upload-section">
                            <h3>上传您的旅行素材</h3>
                            <div class="upload-grid">
                                <div class="upload-item">
                                    <i class="fas fa-images"></i>
                                    <p>上传照片</p>
                                    <input type="file" multiple accept="image/*" id="journalImages">
                                </div>
                                <div class="upload-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <p>定位信息</p>
                                    <button class="btn-secondary">获取位置</button>
                                </div>
                                <div class="upload-item">
                                    <i class="fas fa-edit"></i>
                                    <p>文字笔记</p>
                                    <textarea placeholder="记录您的感受..."></textarea>
                                </div>
                            </div>
                            <button class="btn-primary generate-journal">生成游记</button>
                        </div>
                        <div class="journal-preview">
                            <h3>游记预览</h3>
                            <div class="journal-template">
                                <div class="journal-cover">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y0ZjRmNCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lvoXkuIrkvKDlm77niYc8L3RleHQ+PC9zdmc+" alt="游记封面">
                                    <h4>我的合肥之旅</h4>
                                </div>
                                <div class="journal-content">
                                    <p>AI将根据您的照片、位置和笔记自动生成精美的游记内容...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 积分中心页面 -->
        <section id="points" class="section">
            <div class="container">
                <h1 class="section-title">积分生态中心</h1>

                <!-- 积分概览 -->
                <div class="points-overview">
                    <div class="points-card main">
                        <div class="points-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="points-info">
                            <h2>我的积分</h2>
                            <p class="points-balance">1,250</p>
                            <span class="points-level">黄金会员</span>
                        </div>
                        <div class="points-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 75%"></div>
                            </div>
                            <small>距离白金会员还需750积分</small>
                        </div>
                    </div>
                </div>

                <!-- 积分获取 -->
                <div class="points-earn">
                    <h2>积分获取</h2>
                    <div class="earn-methods">
                        <div class="earn-item">
                            <div class="earn-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="earn-info">
                                <h3>上传游记</h3>
                                <p>分享您的旅行体验</p>
                                <span class="earn-points">+50积分</span>
                            </div>
                            <button class="btn-secondary">去上传</button>
                        </div>
                        <div class="earn-item">
                            <div class="earn-icon">
                                <i class="fas fa-map-pin"></i>
                            </div>
                            <div class="earn-info">
                                <h3>景点签到</h3>
                                <p>到达景点即可获得</p>
                                <span class="earn-points">+10积分</span>
                            </div>
                            <button class="btn-secondary">立即签到</button>
                        </div>
                        <div class="earn-item">
                            <div class="earn-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="earn-info">
                                <h3>评价设施</h3>
                                <p>为景区设施评分</p>
                                <span class="earn-points">+20积分</span>
                            </div>
                            <button class="btn-secondary">去评价</button>
                        </div>
                    </div>
                </div>

                <!-- 积分兑换 -->
                <div class="points-exchange">
                    <h2>积分兑换</h2>
                    <div class="exchange-grid">
                        <div class="exchange-item">
                            <div class="exchange-image">
                                <i class="fas fa-hot-air-balloon"></i>
                            </div>
                            <h3>热气球体验券</h3>
                            <p class="exchange-desc">骆岗公园热气球观光体验</p>
                            <div class="exchange-price">
                                <span class="points-cost">1000积分</span>
                                <span class="original-price">原价¥200</span>
                            </div>
                            <button class="btn-primary">立即兑换</button>
                        </div>
                        <div class="exchange-item">
                            <div class="exchange-image">
                                <i class="fas fa-ice-cream"></i>
                            </div>
                            <h3>文创冰淇淋</h3>
                            <p class="exchange-desc">合肥特色文创冰淇淋</p>
                            <div class="exchange-price">
                                <span class="points-cost">300积分</span>
                                <span class="original-price">原价¥25</span>
                            </div>
                            <button class="btn-primary">立即兑换</button>
                        </div>
                        <div class="exchange-item">
                            <div class="exchange-image">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <h3>咖啡优惠券</h3>
                            <p class="exchange-desc">园内咖啡厅8折优惠</p>
                            <div class="exchange-price">
                                <span class="points-cost">150积分</span>
                                <span class="original-price">8折优惠</span>
                            </div>
                            <button class="btn-primary">立即兑换</button>
                        </div>
                    </div>
                </div>

                <!-- 积分记录 -->
                <div class="points-history">
                    <h2>积分记录</h2>
                    <div class="history-list">
                        <div class="history-item">
                            <div class="history-icon earn">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="history-info">
                                <h4>景点签到</h4>
                                <p>骆岗公园签到成功</p>
                                <span class="history-time">2小时前</span>
                            </div>
                            <span class="history-points earn">+10</span>
                        </div>
                        <div class="history-item">
                            <div class="history-icon spend">
                                <i class="fas fa-minus"></i>
                            </div>
                            <div class="history-info">
                                <h4>兑换商品</h4>
                                <p>文创冰淇淋兑换</p>
                                <span class="history-time">1天前</span>
                            </div>
                            <span class="history-points spend">-300</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- AI助手对话框 -->
    <div class="ai-modal" id="aiModal">
        <div class="ai-modal-content">
            <div class="ai-modal-header">
                <h3>智能助手</h3>
                <button class="close-btn" id="closeAI">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="ai-modal-body">
                <div class="ai-suggestions">
                    <p>您可以问我：</p>
                    <button class="suggestion-btn">骆岗公园怎么去？</button>
                    <button class="suggestion-btn">今天有什么活动？</button>
                    <button class="suggestion-btn">推荐亲子游路线</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
